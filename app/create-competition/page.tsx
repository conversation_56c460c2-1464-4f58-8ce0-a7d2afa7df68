'use client';

import { But<PERSON>, Text, Heading, <PERSON>con<PERSON>utton, TextField } from 'frosted-ui';
import { useRouter } from 'next/navigation';

export default function CreateCompetitionPage() {
  const router = useRouter();

  return (
    <div className="min-h-screen bg-black text-white">
      {/* Top Blue Glow Navigation Bar */}
      <div className="relative bg-black border-b border-blue-500/30">
        <div className="absolute inset-0 bg-blue-500/10 blur-xl"></div>
        <div className="relative px-4 py-3 flex items-center">
          <IconButton variant="ghost" size="2" onClick={() => router.back()}>
            <svg className="w-5 h-5 rotate-180" fill="currentColor" viewBox="0 0 24 24">
              <path d="M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z"/>
            </svg>
          </IconButton>
          <div className="flex-1 text-center">
            <Text size="4" weight="medium" className="text-blue-400">Create Competition</Text>
            <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-blue-500 shadow-[0_0_15px_rgba(59,130,246,0.8)]"></div>
          </div>
        </div>
      </div>

      {/* Form Content */}
      <div className="max-w-md mx-auto px-4 py-8 space-y-6">
        <div className="text-center mb-8">
          <Heading size="6" weight="bold" className="text-white mb-2">
            Create New Competition
          </Heading>
          <Text size="3" className="text-gray-400">
            Set up a new competition for your community
          </Text>
        </div>

        {/* Form Fields */}
        <div className="space-y-4">
          <div>
            <Text size="2" weight="medium" className="text-white mb-2 block">
              Competition Title
            </Text>
            <TextField 
              placeholder="e.g., Generate 100K views"
              className="w-full"
            />
          </div>

          <div>
            <Text size="2" weight="medium" className="text-white mb-2 block">
              Prize Amount
            </Text>
            <TextField 
              placeholder="e.g., $1,000"
              className="w-full"
            />
          </div>

          <div>
            <Text size="2" weight="medium" className="text-white mb-2 block">
              End Date
            </Text>
            <TextField 
              placeholder="e.g., Feb 18th"
              className="w-full"
            />
          </div>

          <div>
            <Text size="2" weight="medium" className="text-white mb-2 block">
              Description
            </Text>
            <TextField 
              placeholder="Describe the competition rules..."
              className="w-full"
            />
          </div>
        </div>

        {/* Action Buttons */}
        <div className="space-y-3 pt-6">
          <Button size="4" className="w-full">
            Create Competition
          </Button>
          
          <Button size="4" variant="outline" className="w-full" onClick={() => router.back()}>
            Cancel
          </Button>
        </div>
      </div>
    </div>
  );
}
