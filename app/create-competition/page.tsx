'use client';

import { But<PERSON>, Text, Heading, IconButton, TextArea } from 'frosted-ui';
import { useRouter } from 'next/navigation';

export default function CreateCompetitionPage() {
  const router = useRouter();

  return (
    <div className="min-h-screen bg-black text-white">
      {/* Top Blue Glow Navigation Bar */}
      <div className="relative bg-black border-b border-blue-500/30">
        <div className="absolute inset-0 bg-blue-500/10 blur-xl"></div>
        <div className="relative px-4 py-3 flex items-center">
          <IconButton variant="ghost" size="2" onClick={() => router.back()}>
            <svg className="w-5 h-5 rotate-180" fill="currentColor" viewBox="0 0 24 24">
              <path d="M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z"/>
            </svg>
          </IconButton>
          <div className="flex-1 text-center">
            <Text size="4" weight="medium" className="text-blue-400">Create Competition</Text>
          </div>
        </div>
      </div>

      {/* Form Content */}
      <div className="max-w-md mx-auto px-4 py-8 space-y-6">
        <div className="text-center mb-8">
          <Heading size="6" weight="bold" className="text-white mb-2">
            Create New Competition
          </Heading>
          <Text size="3" className="text-gray-400">
            Set up a new competition for your community
          </Text>
        </div>

        {/* Form Fields */}
        <div className="space-y-4">
          <div>
            <Text size="2" weight="medium" className="text-white mb-2 block">
              Competition Title
            </Text>
            <input
              type="text"
              placeholder="e.g., Generate 100K views"
              className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          <div>
            <Text size="2" weight="medium" className="text-white mb-2 block">
              Prize Amount
            </Text>
            <input
              type="text"
              placeholder="e.g., $1,000"
              className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          <div>
            <Text size="2" weight="medium" className="text-white mb-2 block">
              End Date
            </Text>
            <input
              type="text"
              placeholder="e.g., Feb 18th"
              className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          <div>
            <Text size="2" weight="medium" className="text-white mb-2 block">
              Description
            </Text>
            <TextArea
              placeholder="Describe the competition rules..."
              className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
        </div>

        {/* Action Buttons */}
        <div className="space-y-3 pt-6">
          <Button size="4" className="w-full">
            Create Competition
          </Button>
          
          <Button size="4" variant="outline" className="w-full" onClick={() => router.back()}>
            Cancel
          </Button>
        </div>
      </div>
    </div>
  );
}
