'use client';

import { useState } from 'react';
import { Button } from 'frosted-ui';
import CreateCompetitionModal from '../components/CreateCompetitionModal';

export default function CreateCompetitionPage() {
  const [isModalOpen, setIsModalOpen] = useState(false);

  return (
    <div className="min-h-screen bg-black text-white relative overflow-hidden">
      {/* Blue glow particles background */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute top-20 left-10 w-32 h-32 bg-blue-500/20 rounded-full blur-xl animate-pulse"></div>
        <div className="absolute top-40 right-20 w-24 h-24 bg-blue-400/15 rounded-full blur-lg animate-pulse delay-1000"></div>
        <div className="absolute bottom-40 left-1/4 w-40 h-40 bg-blue-600/10 rounded-full blur-2xl animate-pulse delay-2000"></div>
        <div className="absolute bottom-20 right-1/3 w-28 h-28 bg-blue-300/20 rounded-full blur-xl animate-pulse delay-500"></div>
      </div>

      <div className="max-w-4xl mx-auto px-4 py-16 relative z-10">
        <div className="text-center">
          <h1 className="text-5xl font-bold text-white mb-8 relative">
            <span className="relative z-10">Create Competition</span>
            <div className="absolute inset-0 text-blue-400 blur-lg opacity-50">Create Competition</div>
          </h1>

          <p className="text-white/70 text-xl mb-12">
            Design your competition with clean forms and bright glows
          </p>

          <Button
            onClick={() => setIsModalOpen(true)}
            className="bg-blue-500 hover:bg-blue-600 text-white px-8 py-4 rounded-xl font-semibold text-lg relative overflow-hidden"
          >
            <span className="relative z-10">Open Competition Creator</span>
            <div className="absolute inset-0 bg-blue-400/20 blur-xl"></div>
          </Button>

          <div className="mt-16 grid md:grid-cols-2 gap-8">
            <div className="bg-white/5 backdrop-blur-sm rounded-xl p-6 border border-white/10">
              <h3 className="text-xl font-semibold text-white mb-4">Mobile Design</h3>
              <p className="text-white/60">Optimized for mobile devices with touch-friendly controls and responsive layout</p>
            </div>

            <div className="bg-white/5 backdrop-blur-sm rounded-xl p-6 border border-white/10">
              <h3 className="text-xl font-semibold text-white mb-4">Desktop Design</h3>
              <p className="text-white/60">Full-featured desktop experience with advanced form controls and validation</p>
            </div>
          </div>
        </div>
      </div>

      <CreateCompetitionModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
      />
    </div>
  );
}