'use client';

import { useState } from 'react';
import { <PERSON><PERSON>, <PERSON> } from 'frosted-ui';

interface CreateCompetitionModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export default function CreateCompetitionModal({ isOpen, onClose }: CreateCompetitionModalProps) {
  const [competitionType, setCompetitionType] = useState('money-earned');
  const [startDate, setStartDate] = useState('');
  const [endDate, setEndDate] = useState('');
  const [startTime, setStartTime] = useState('');
  const [endTime, setEndTime] = useState('');
  const [prizes, setPrizes] = useState([
    { place: '1st place', type: 'cash', amount: '', custom: '' },
    { place: '2nd place', type: 'cash', amount: '', custom: '' }
  ]);

  const addPrize = () => {
    setPrizes([...prizes, { place: `${prizes.length + 1}${getOrdinalSuffix(prizes.length + 1)} place`, type: 'cash', amount: '', custom: '' }]);
  };

  const getOrdinalSuffix = (num: number) => {
    const j = num % 10;
    const k = num % 100;
    if (j === 1 && k !== 11) return 'st';
    if (j === 2 && k !== 12) return 'nd';
    if (j === 3 && k !== 13) return 'rd';
    return 'th';
  };

  const removePrize = (index: number) => {
    setPrizes(prizes.filter((_, i) => i !== index));
  };

  const updatePrize = (index: number, field: string, value: string) => {
    const newPrizes = [...prizes];
    newPrizes[index] = { ...newPrizes[index], [field]: value };
    setPrizes(newPrizes);
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <Card className="w-full max-w-md bg-gray-900 border-gray-700 max-h-[90vh] overflow-y-auto">
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-gray-700">
            <h2 className="text-xl font-semibold text-white">Competition details</h2>
            <Button
              variant="ghost"
              onClick={onClose}
              className="text-gray-400 hover:text-white p-2"
            >
              ✕
            </Button>
          </div>

          <div className="p-6 space-y-6">
            {/* Competition Type */}
            <div>
              <label className="block text-sm font-medium text-white mb-2">Competition Type</label>
              <Select value={competitionType} onValueChange={setCompetitionType}>
                <option value="money-earned">Money earned</option>
                <option value="views-generated">Views generated</option>
                <option value="users-invited">Users invited</option>
              </Select>
            </div>

            {/* Start Date */}
            <div>
              <label className="block text-sm font-medium text-white mb-2">Start date</label>
              <div className="grid grid-cols-2 gap-3">
                <div>
                  <input
                    type="date"
                    value={startDate}
                    onChange={(e) => setStartDate(e.target.value)}
                    className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:border-blue-500 focus:ring-1 focus:ring-blue-500"
                    placeholder="Pick a date"
                  />
                </div>
                <div>
                  <input
                    type="time"
                    value={startTime}
                    onChange={(e) => setStartTime(e.target.value)}
                    className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:border-blue-500 focus:ring-1 focus:ring-blue-500"
                    placeholder="Choose time"
                  />
                </div>
              </div>
            </div>

            {/* End Date */}
            <div>
              <label className="block text-sm font-medium text-white mb-2">End date</label>
              <div className="grid grid-cols-2 gap-3">
                <div>
                  <input
                    type="date"
                    value={endDate}
                    onChange={(e) => setEndDate(e.target.value)}
                    className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:border-blue-500 focus:ring-1 focus:ring-blue-500"
                    placeholder="Pick a date"
                  />
                </div>
                <div>
                  <input
                    type="time"
                    value={endTime}
                    onChange={(e) => setEndTime(e.target.value)}
                    className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:border-blue-500 focus:ring-1 focus:ring-blue-500"
                    placeholder="Choose time"
                  />
                </div>
              </div>
            </div>

            {/* Prizes */}
            <div>
              <label className="block text-sm font-medium text-white mb-4">Prizes</label>
              <div className="space-y-4">
                {prizes.map((prize, index) => (
                  <div key={index} className="space-y-3">
                    <div className="flex items-center justify-between">
                      <span className="text-white font-medium">{prize.place}</span>
                      {prizes.length > 1 && (
                        <Button
                          variant="ghost"
                          onClick={() => removePrize(index)}
                          className="text-red-400 hover:text-red-300 p-1"
                        >
                          🗑️
                        </Button>
                      )}
                    </div>

                    <div className="grid grid-cols-2 gap-2">
                      <Button
                        variant={prize.type === 'cash' ? 'solid' : 'ghost'}
                        onClick={() => updatePrize(index, 'type', 'cash')}
                        className={`${prize.type === 'cash' ? 'bg-gray-700 text-white' : 'text-gray-400'}`}
                      >
                        Cash
                      </Button>
                      <Button
                        variant={prize.type === 'custom' ? 'solid' : 'ghost'}
                        onClick={() => updatePrize(index, 'type', 'custom')}
                        className={`${prize.type === 'custom' ? 'bg-gray-700 text-white' : 'text-gray-400'}`}
                      >
                        Custom
                      </Button>
                    </div>

                    {prize.type === 'cash' ? (
                      <input
                        type="text"
                        value={prize.amount}
                        onChange={(e) => updatePrize(index, 'amount', e.target.value)}
                        placeholder="Enter $ amount"
                        className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:border-blue-500 focus:ring-1 focus:ring-blue-500"
                      />
                    ) : (
                      <div className="space-y-2">
                        <input
                          type="text"
                          value={prize.custom}
                          onChange={(e) => updatePrize(index, 'custom', e.target.value)}
                          placeholder="Orange Lamborghini Aventador"
                          className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:border-blue-500 focus:ring-1 focus:ring-blue-500"
                        />
                        <div className="flex items-center justify-between">
                          <span className="text-xs text-gray-400">lambo_0001.png</span>
                          <div className="flex space-x-2">
                            <Button variant="ghost" className="text-blue-400 text-xs">Replace</Button>
                            <Button variant="ghost" className="text-red-400 text-xs">Delete</Button>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                ))}

                <Button
                  onClick={addPrize}
                  className="w-full bg-blue-500 hover:bg-blue-600 text-white py-2 rounded-lg flex items-center justify-center space-x-2"
                >
                  <span>Add Prize</span>
                  <span className="text-lg">+</span>
                </Button>
              </div>
            </div>

            {/* Upload Image */}
            <div className="border-2 border-dashed border-gray-600 rounded-lg p-8 text-center">
              <div className="text-gray-400 mb-2">📷</div>
              <p className="text-white font-medium mb-1">Upload image</p>
              <p className="text-xs text-gray-400">We recommend uploading images with a 1:1 aspect ratio</p>
            </div>

            {/* Footer text */}
            <p className="text-xs text-gray-400 text-center">
              Upon creation of your competition we will auto-generate a chat for your users to banter in.
            </p>

            {/* Create Button */}
            <Button
              size="4"
              className="w-full text-white py-3 font-semibold text-lg relative overflow-hidden"
              style={{
                background: 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)',
                boxShadow: '0 0 25px rgba(59, 130, 246, 0.6), 0 0 50px rgba(59, 130, 246, 0.4)',
                border: '1px solid rgba(59, 130, 246, 0.3)'
              }}
            >
              <span className="relative z-10">Create Competition</span>
            </Button>
          </div>
        </Card>
    </div>
  );
}