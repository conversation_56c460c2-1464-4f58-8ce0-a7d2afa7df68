'use client';

import { ChevronRightIcon } from 'frosted-ui/icons';
import { Button, Card, Avatar, Badge, IconButton } from 'frosted-ui';
import { useRouter } from 'next/navigation';

// Mock data for leaderboard
const leaderboardData = [
  {
    rank: 1,
    name: "<PERSON>",
    username: "@shark",
    avatar: "🦈",
    amount: 25800.00,
    color: "bg-yellow-500"
  },
  {
    rank: 2,
    name: "<PERSON>",
    username: "@methodicalstew",
    avatar: "👨‍💻",
    amount: 21344.50,
    color: "bg-gray-400"
  },
  {
    rank: 3,
    name: "<PERSON>haq",
    username: "@shaq4257",
    avatar: "🏀",
    amount: 14565.30,
    color: "bg-orange-500"
  },
  {
    rank: 4,
    name: "<PERSON><PERSON>",
    username: "@ilyamiskov",
    avatar: "👤",
    amount: 13316.00,
    color: "bg-blue-500"
  },
  {
    rank: 5,
    name: "<PERSON>v<PERSON><PERSON>",
    username: "@savnatra",
    avatar: "💎",
    amount: 11131.38,
    color: "bg-green-500"
  },
  {
    rank: 6,
    name: "<PERSON>",
    username: "@user673297",
    avatar: "🎯",
    amount: 9620.90,
    color: "bg-purple-500"
  },
  {
    rank: 7,
    name: "Amirah Robinson",
    username: "@amirahgirl",
    avatar: "👩",
    amount: 8760.00,
    color: "bg-pink-500"
  },
  {
    rank: 8,
    name: "AB",
    username: "@abonsocials",
    avatar: "📱",
    amount: 8105.30,
    color: "bg-indigo-500"
  },
  {
    rank: 9,
    name: "Savnatra",
    username: "@savnatra",
    avatar: "💎",
    amount: 7677.10,
    color: "bg-teal-500"
  }
];

const tabs = [
  { id: 'money', label: 'Money earned', active: true },
  { id: 'views', label: 'Views generated', active: false },
  { id: 'more', label: 'More', active: false }
];

export default function LeaderboardPage() {
  const router = useRouter();

  return (
    <div className="min-h-screen bg-black text-white relative overflow-hidden">
      {/* Blue glow particles background */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute top-20 left-10 w-32 h-32 bg-blue-500/20 rounded-full blur-xl animate-pulse"></div>
        <div className="absolute top-40 right-20 w-24 h-24 bg-blue-400/15 rounded-full blur-lg animate-pulse delay-1000"></div>
        <div className="absolute bottom-40 left-1/4 w-40 h-40 bg-blue-600/10 rounded-full blur-2xl animate-pulse delay-2000"></div>
        <div className="absolute bottom-20 right-1/3 w-28 h-28 bg-blue-300/20 rounded-full blur-xl animate-pulse delay-500"></div>
      </div>

      {/* Status bar */}
      <div className="flex justify-between items-center px-6 py-3 text-sm">
        <span>9:41</span>
        <div className="flex items-center space-x-1">
          <div className="flex space-x-1">
            <div className="w-1 h-3 bg-white rounded-full"></div>
            <div className="w-1 h-3 bg-white rounded-full"></div>
            <div className="w-1 h-3 bg-white rounded-full"></div>
            <div className="w-1 h-3 bg-white/50 rounded-full"></div>
          </div>
          <div className="w-6 h-3 border border-white rounded-sm">
            <div className="w-4 h-full bg-white rounded-sm"></div>
          </div>
        </div>
      </div>

      {/* Header */}
      <div className="flex items-center justify-between px-6 py-4 relative z-10">
        <IconButton
          onClick={() => router.back()}
          variant="ghost"
          className="text-white hover:bg-white/10"
        >
          <ChevronRightIcon className="w-6 h-6 rotate-180" />
        </IconButton>
        <div className="flex space-x-8">
          <Button variant="ghost" className="text-white/60 hover:text-white flex items-center">
            Competitions
            <Badge className="ml-2 bg-blue-500 text-white">1</Badge>
          </Button>
          <Button variant="ghost" className="text-white border-b-2 border-blue-500 pb-1">
            Leaderboard
          </Button>
        </div>
      </div>

      {/* Tabs */}
      <div className="px-6 py-4 relative z-10">
        <div className="flex space-x-6">
          {tabs.map((tab) => (
            <Button
              key={tab.id}
              variant={tab.active ? "soft" : "ghost"}
              className={`flex items-center space-x-2 text-sm ${
                tab.active
                  ? 'bg-white/10 text-white'
                  : 'text-white/60 hover:text-white'
              }`}
            >
              {tab.id === 'money' && <span className="text-yellow-400">💰</span>}
              {tab.id === 'views' && <span className="text-orange-400">👁️</span>}
              {tab.id === 'more' && <span className="text-purple-400">👥</span>}
              <span>{tab.label}</span>
            </Button>
          ))}
        </div>
      </div>

      {/* Money earned title */}
      <div className="px-6 py-2 relative z-10">
        <h1 className="text-2xl font-bold">Money earned</h1>
      </div>

      {/* Leaderboard */}
      <div className="px-6 pb-8 relative z-10">
        <div className="space-y-3">
          {leaderboardData.map((user) => (
            <Card
              key={`${user.rank}-${user.username}`}
              className="bg-white/5 backdrop-blur-sm border-white/10 hover:bg-white/10 transition-colors p-4"
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <Badge className={`${user.color} text-white text-sm font-bold w-8 h-8 rounded-full flex items-center justify-center`}>
                    {user.rank}
                  </Badge>
                  <Avatar
                    className="w-12 h-12"
                    fallback={user.avatar}
                  />
                  <div>
                    <div className="font-semibold text-white">{user.name}</div>
                    <div className="text-sm text-white/60">{user.username}</div>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="text-green-400 text-lg font-bold">
                    ${user.amount.toLocaleString('en-US', { minimumFractionDigits: 2 })}
                  </span>
                </div>
              </div>
            </Card>
          ))}
        </div>
      </div>

      {/* Bottom indicator */}
      <div className="flex justify-center pb-4">
        <div className="w-32 h-1 bg-white/30 rounded-full"></div>
      </div>
    </div>
  );
}