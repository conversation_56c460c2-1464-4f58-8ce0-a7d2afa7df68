'use client';

import { Avatar, Badge, Text, Heading, IconButton } from 'frosted-ui';
import { useRouter } from 'next/navigation';

// Mock data for competition leaderboard
const leaderboardData = [
  {
    rank: 1,
    name: "<PERSON>",
    username: "@shark",
    avatar: "🦈",
    amount: 26000.00,
    isVerified: true
  },
  {
    rank: 2,
    name: "<PERSON>",
    username: "@methodicalstew",
    avatar: "👨‍💻",
    amount: 21345.50,
    isVerified: false
  },
  {
    rank: 3,
    name: "<PERSON>ha<PERSON>",
    username: "@shaq4257",
    avatar: "🏀",
    amount: 14598.30,
    isVerified: false
  },
  {
    rank: 4,
    name: "<PERSON><PERSON>",
    username: "@ilyam<PERSON>ov",
    avatar: "💎",
    amount: 13196.00,
    isVerified: true
  },
  {
    rank: 5,
    name: "Savnatra",
    username: "@savnatra",
    avatar: "⭐",
    amount: 11432.26,
    isVerified: false
  },
  {
    rank: 6,
    name: "<PERSON>",
    username: "@user673297",
    avatar: "🎯",
    amount: 9200.00,
    isVerified: false
  },
  {
    rank: 7,
    name: "<PERSON><PERSON>",
    username: "@amiright",
    avatar: "👩‍💼",
    amount: 5190.00,
    isVerified: false
  },
  {
    rank: 8,
    name: "AB",
    username: "@abconsults",
    avatar: "🔥",
    amount: 4105.30,
    isVerified: false
  }
];

export default function LeaderboardPage() {
  const router = useRouter();

  return (
    <div className="min-h-screen bg-black text-white">
      {/* Top Blue Glow Navigation Bar */}
      <div className="relative bg-black border-b border-blue-500/30">
        <div className="absolute inset-0 bg-blue-500/10 blur-xl"></div>
        <div className="relative px-4 py-3 flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <IconButton variant="ghost" size="2" onClick={() => router.back()}>
              <svg className="w-5 h-5 rotate-180" fill="currentColor" viewBox="0 0 24 24">
                <path d="M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z"/>
              </svg>
            </IconButton>
            <Text size="4" weight="medium">Competitions</Text>
            <Badge size="1">1</Badge>
          </div>
          <div className="relative">
            <Text size="4" weight="medium" className="text-blue-400">Leaderboard</Text>
          </div>
        </div>
      </div>

      {/* Competition Type Filters */}
      <div className="flex items-center space-x-2 px-4 py-3 border-b border-gray-800">
        <Badge variant="solid">
          💰 Money earned
        </Badge>
        <Badge variant="outline">
          👁️ Views generated
        </Badge>
        <Badge variant="outline">
          👥 Users invited
        </Badge>
      </div>

      {/* Money earned title */}
      <div className="px-4 py-4">
        <Heading size="6" weight="bold" className="text-white">Money earned</Heading>
      </div>

      {/* Leaderboard */}
      <div className="px-4 space-y-1">
        {leaderboardData.map((user) => (
          <div key={`${user.rank}-${user.username}`} className="flex items-center justify-between py-3 px-2 hover:bg-gray-800/50 rounded-lg transition-colors">
            <div className="flex items-center space-x-3">
              {/* Rank Badge */}
              <div className={`w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold ${
                user.rank === 1 ? 'bg-yellow-500 text-black' :
                user.rank === 2 ? 'bg-gray-400 text-white' :
                user.rank === 3 ? 'bg-orange-500 text-white' :
                'bg-blue-500 text-white'
              }`}>
                {user.rank}
              </div>

              {/* Avatar */}
              <Avatar size="3" fallback={user.avatar} />

              {/* User Info */}
              <div>
                <div className="flex items-center space-x-1">
                  <Text size="3" weight="medium" className="text-white">{user.name}</Text>
                  {user.isVerified && (
                    <svg className="w-4 h-4 text-blue-400" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                    </svg>
                  )}
                </div>
                <Text size="2" className="text-gray-400">{user.username}</Text>
              </div>
            </div>

            {/* Amount */}
            <div className="flex items-center space-x-1">
              <svg className="w-4 h-4 text-green-400" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
              </svg>
              <Text size="3" weight="bold" className="text-green-400">
                ${user.amount.toLocaleString('en-US', { minimumFractionDigits: 2 })}
              </Text>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}