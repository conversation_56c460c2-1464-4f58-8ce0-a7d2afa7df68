'use client';

import { But<PERSON>, <PERSON>, IconButton, Card, Avatar, Heading } from 'frosted-ui';
import { useRouter } from 'next/navigation';

// Mock data for active competitions
const competitionsData = [
  {
    id: 1,
    title: "Generate 100K views",
    status: "Ended on",
    endDate: "Feb 5th",
    winner: "<PERSON><PERSON>",
    winnerAvatar: "💎",
    winnerUsername: "@ilyamiskov",
    prize: "$1,000",
    participants: 15
  },
  {
    id: 2,
    title: "Invite 15 users",
    status: "Ended on",
    endDate: "Feb 5th", 
    winner: "<PERSON><PERSON>",
    winnerAvatar: "💎",
    winnerUsername: "@ilyamiskov",
    prize: "$500",
    participants: 8
  }
];

export default function CompetitionsWithDataPage() {
  const router = useRouter();

  return (
    <div className="min-h-screen bg-black text-white">
      {/* Header Navigation */}
      <div className="flex items-center justify-between px-4 py-3 border-b border-gray-800">
        <div className="flex items-center space-x-2">
          <IconButton variant="ghost" size="2" onClick={() => router.back()}>
            <svg className="w-5 h-5 rotate-180" fill="currentColor" viewBox="0 0 24 24">
              <path d="M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z"/>
            </svg>
          </IconButton>
          <div className="relative">
            <Text size="4" weight="medium" className="text-blue-400">Competitions</Text>
            <div className="absolute -bottom-3 left-0 right-0 h-0.5 bg-blue-500 rounded-full shadow-[0_0_8px_rgba(59,130,246,0.8)]"></div>
          </div>
        </div>
        <Text size="4" weight="medium" className="text-gray-400">Leaderboard</Text>
      </div>

      <div className="px-4 py-6">
        {/* Upcoming section */}
        <div className="mb-8">
          <Heading size="5" weight="bold" className="text-white mb-4">Upcoming</Heading>
          <Text size="3" className="text-gray-400">There are no upcoming competitions yet.</Text>
        </div>

        {/* Past section */}
        <div className="mb-8">
          <Heading size="5" weight="bold" className="text-white mb-2">Past</Heading>
          <Text size="2" className="text-gray-400 mb-6">Win condition</Text>
          
          <div className="space-y-4">
            {competitionsData.map((competition) => (
              <Card key={competition.id} className="bg-gray-900/50 border-gray-700 p-4 hover:bg-gray-800/50 transition-colors">
                <div className="flex items-center justify-between mb-3">
                  <Text size="3" weight="medium" className="text-white">{competition.title}</Text>
                  <div className="flex items-center space-x-2">
                    <Text size="1" className="text-gray-400">{competition.status}</Text>
                    <Text size="1" className="text-white">{competition.endDate}</Text>
                    <svg className="w-4 h-4 text-gray-400" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M12 8c1.1 0 2-.9 2-2s-.9-2-2-2-2 .9-2 2 .9 2 2 2zm0 2c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm0 6c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2z"/>
                    </svg>
                  </div>
                </div>
                
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <Avatar size="2" fallback={competition.winnerAvatar} />
                    <div>
                      <Text size="2" weight="medium" className="text-white">{competition.winner}</Text>
                      <Text size="1" className="text-gray-400">{competition.winnerUsername}</Text>
                    </div>
                  </div>
                  
                  <div className="text-right">
                    <Text size="2" weight="bold" className="text-green-400">{competition.prize}</Text>
                    <Text size="1" className="text-gray-400">{competition.participants} participants</Text>
                  </div>
                </div>
              </Card>
            ))}
          </div>
        </div>

        {/* Create competition button */}
        <Button 
          size="4"
          className="text-white px-8 py-4 font-semibold w-full"
          style={{
            background: 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)',
            boxShadow: '0 0 25px rgba(59, 130, 246, 0.6), 0 0 50px rgba(59, 130, 246, 0.4)',
            border: '1px solid rgba(59, 130, 246, 0.3)'
          }}
        >
          Create competition
        </Button>
      </div>
    </div>
  );
}
