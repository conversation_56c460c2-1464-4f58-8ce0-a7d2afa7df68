'use client';

import { ChevronRightIcon } from 'frosted-ui/icons';
import { <PERSON><PERSON>, IconButton, Card, Badge } from 'frosted-ui';
import { useRouter } from 'next/navigation';

// Mock data for competitions
const upcomingCompetitions = [
  {
    id: 1,
    title: "Generate 100K views",
    status: "Win condition",
    endDate: "Feb 18th",
    winner: "<PERSON><PERSON>skov"
  }
];

const pastCompetitions = [
  {
    id: 2,
    title: "Invite 15 users",
    status: "Win condition",
    endDate: "Feb 18th",
    winner: "<PERSON><PERSON> Miskov"
  }
];

export default function CompetitionsWithDataPage() {
  const router = useRouter();

  return (
    <div className="min-h-screen bg-black text-white relative overflow-hidden">
      {/* Blue glow particles background */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute top-20 left-10 w-32 h-32 bg-blue-500/20 rounded-full blur-xl animate-pulse"></div>
        <div className="absolute top-40 right-20 w-24 h-24 bg-blue-400/15 rounded-full blur-lg animate-pulse delay-1000"></div>
        <div className="absolute bottom-40 left-1/4 w-40 h-40 bg-blue-600/10 rounded-full blur-2xl animate-pulse delay-2000"></div>
        <div className="absolute bottom-20 right-1/3 w-28 h-28 bg-blue-300/20 rounded-full blur-xl animate-pulse delay-500"></div>
      </div>

      {/* Status bar */}
      <div className="flex justify-between items-center px-6 py-3 text-sm">
        <span>9:41</span>
        <div className="flex items-center space-x-1">
          <div className="flex space-x-1">
            <div className="w-1 h-3 bg-white rounded-full"></div>
            <div className="w-1 h-3 bg-white rounded-full"></div>
            <div className="w-1 h-3 bg-white rounded-full"></div>
            <div className="w-1 h-3 bg-white/50 rounded-full"></div>
          </div>
          <div className="w-6 h-3 border border-white rounded-sm">
            <div className="w-4 h-full bg-white rounded-sm"></div>
          </div>
        </div>
      </div>

      {/* Header */}
      <div className="flex items-center justify-between px-6 py-4 relative z-10">
        <IconButton
          onClick={() => router.back()}
          variant="ghost"
          className="text-white hover:bg-white/10"
        >
          <ChevronRightIcon className="w-6 h-6 rotate-180" />
        </IconButton>
        <div className="flex space-x-8">
          <Button variant="ghost" className="text-white border-b-2 border-blue-500 pb-1">
            Competitions
          </Button>
          <Button variant="ghost" className="text-white/60 hover:text-white">
            Leaderboard
          </Button>
        </div>
      </div>

      {/* Content */}
      <div className="px-6 pb-8 relative z-10">
        {/* Upcoming Section */}
        <div className="mb-8">
          <h2 className="text-2xl font-bold text-white mb-4">Upcoming</h2>
          <p className="text-white/60 text-sm mb-4">There are no upcoming competitions yet.</p>
        </div>

        {/* Past Section */}
        <div className="mb-8">
          <h2 className="text-2xl font-bold text-white mb-4">Past</h2>
          <div className="space-y-4">
            {pastCompetitions.map((competition) => (
              <Card
                key={competition.id}
                className="bg-white/5 backdrop-blur-sm border-white/10 p-4"
              >
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center space-x-3">
                    <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                    <span className="text-white/60 text-sm">{competition.status}</span>
                  </div>
                  <Button variant="ghost" className="text-white/60 p-1">
                    <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M12 8c1.1 0 2-.9 2-2s-.9-2-2-2-2 .9-2 2 .9 2 2 2zm0 2c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm0 6c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2z"/>
                    </svg>
                  </Button>
                </div>

                <h3 className="text-white font-semibold text-lg mb-2">{competition.title}</h3>

                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-white/60 text-sm">Ended on</p>
                    <p className="text-white text-sm">{competition.endDate}</p>
                  </div>
                  <div className="text-right">
                    <p className="text-white/60 text-sm">Winner</p>
                    <div className="flex items-center space-x-2">
                      <div className="w-1 h-1 bg-blue-500 rounded-full"></div>
                      <p className="text-white text-sm">{competition.winner}</p>
                    </div>
                  </div>
                </div>
              </Card>
            ))}
          </div>
        </div>

        {/* Create competition button */}
        <Button
          className="bg-blue-500 hover:bg-blue-600 text-white px-8 py-3 rounded-xl font-semibold w-full"
        >
          Create competition
        </Button>
      </div>

      {/* Bottom indicator */}
      <div className="flex justify-center pb-4">
        <div className="w-32 h-1 bg-white/30 rounded-full"></div>
      </div>
    </div>
  );
}