'use client';

import { But<PERSON>, Text, IconButton } from 'frosted-ui';
import { useRouter } from 'next/navigation';

export default function CompetitionsPage() {
  const router = useRouter();

  return (
    <div className="min-h-screen bg-black text-white">
      {/* Header Navigation */}
      <div className="flex items-center justify-between px-4 py-3 border-b border-gray-800">
        <div className="flex items-center space-x-2">
          <IconButton variant="ghost" size="2" onClick={() => router.back()}>
            <svg className="w-5 h-5 rotate-180" fill="currentColor" viewBox="0 0 24 24">
              <path d="M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z"/>
            </svg>
          </IconButton>
          <div className="relative">
            <Text size="4" weight="medium" className="text-blue-400">Competitions</Text>

          </div>
        </div>
        <Text size="4" weight="medium" className="text-gray-400">Leaderboard</Text>
      </div>

      {/* Empty State Content */}
      <div className="flex flex-col items-center justify-center px-6 py-16 relative z-10">
        {/* Blue glowing chart icon */}
        <div className="relative mb-8">
          <div className="w-24 h-24 bg-blue-500/20 rounded-2xl flex items-center justify-center backdrop-blur-sm border border-blue-400/30">
            <div className="w-12 h-12 flex items-center justify-center">
              {/* Chart bars icon */}
              <svg className="w-8 h-8 text-blue-400" fill="currentColor" viewBox="0 0 24 24">
                <path d="M3 13h2v8H3v-8zm4-6h2v14H7V7zm4-4h2v18h-2V3zm4 9h2v9h-2v-9z"/>
              </svg>
            </div>
          </div>
          {/* Glow effect */}
          <div className="absolute inset-0 w-24 h-24 bg-blue-500/10 rounded-2xl blur-xl"></div>
        </div>

        {/* No competitions text */}
        <Text size="8" weight="bold" className="text-white mb-4">No competitions</Text>
        <Text size="4" className="text-gray-400 mb-12">There are no active competitions yet.</Text>

        {/* Create competition button */}
        <Button
          size="4"
          className="text-white px-8 py-3 font-semibold w-full max-w-sm"
          style={{
            background: 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)',
            boxShadow: '0 0 25px rgba(59, 130, 246, 0.6), 0 0 50px rgba(59, 130, 246, 0.4)',
            border: '1px solid rgba(59, 130, 246, 0.3)'
          }}
        >
          Create competition
        </Button>
      </div>

      {/* Bottom indicator */}
      <div className="flex justify-center pb-4 absolute bottom-0 w-full">
        <div className="w-32 h-1 bg-white/30 rounded-full"></div>
      </div>
    </div>
  );
}