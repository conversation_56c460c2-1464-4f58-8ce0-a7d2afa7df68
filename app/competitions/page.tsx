'use client';

import { ChevronRightIcon } from 'frosted-ui/icons';
import { Button, IconButton } from 'frosted-ui';
import { useRouter } from 'next/navigation';

export default function CompetitionsPage() {
  const router = useRouter();

  return (
    <div className="min-h-screen bg-black text-white relative overflow-hidden">
      {/* Blue glow particles background */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute top-20 left-10 w-32 h-32 bg-blue-500/20 rounded-full blur-xl animate-pulse"></div>
        <div className="absolute top-40 right-20 w-24 h-24 bg-blue-400/15 rounded-full blur-lg animate-pulse delay-1000"></div>
        <div className="absolute bottom-40 left-1/4 w-40 h-40 bg-blue-600/10 rounded-full blur-2xl animate-pulse delay-2000"></div>
        <div className="absolute bottom-20 right-1/3 w-28 h-28 bg-blue-300/20 rounded-full blur-xl animate-pulse delay-500"></div>
      </div>

      {/* Status bar */}
      <div className="flex justify-between items-center px-6 py-3 text-sm">
        <span>9:41</span>
        <div className="flex items-center space-x-1">
          <div className="flex space-x-1">
            <div className="w-1 h-3 bg-white rounded-full"></div>
            <div className="w-1 h-3 bg-white rounded-full"></div>
            <div className="w-1 h-3 bg-white rounded-full"></div>
            <div className="w-1 h-3 bg-white/50 rounded-full"></div>
          </div>
          <div className="w-6 h-3 border border-white rounded-sm">
            <div className="w-4 h-full bg-white rounded-sm"></div>
          </div>
        </div>
      </div>

      {/* Header */}
      <div className="flex items-center justify-between px-6 py-4 relative z-10">
        <IconButton
          onClick={() => router.back()}
          variant="ghost"
          className="text-white hover:bg-white/10"
        >
          <ChevronRightIcon className="w-6 h-6 rotate-180" />
        </IconButton>
        <div className="flex space-x-8">
          <Button variant="ghost" className="text-white border-b-2 border-blue-500 pb-1">
            Competitions
          </Button>
          <Button variant="ghost" className="text-white/60 hover:text-white">
            Leaderboard
          </Button>
        </div>
      </div>

      {/* Empty State Content */}
      <div className="flex flex-col items-center justify-center px-6 py-16 relative z-10">
        {/* Blue glowing chart icon */}
        <div className="relative mb-8">
          <div className="w-24 h-24 bg-blue-500/20 rounded-2xl flex items-center justify-center backdrop-blur-sm border border-blue-400/30">
            <div className="w-12 h-12 flex items-center justify-center">
              {/* Chart bars icon */}
              <svg className="w-8 h-8 text-blue-400" fill="currentColor" viewBox="0 0 24 24">
                <path d="M3 13h2v8H3v-8zm4-6h2v14H7V7zm4-4h2v18h-2V3zm4 9h2v9h-2v-9z"/>
              </svg>
            </div>
          </div>
          {/* Glow effect */}
          <div className="absolute inset-0 w-24 h-24 bg-blue-500/10 rounded-2xl blur-xl"></div>
        </div>

        {/* No competitions text */}
        <h1 className="text-2xl font-bold text-white mb-2">No competitions</h1>
        <p className="text-white/60 text-center mb-12 max-w-sm">
          There are no active competitions yet.
        </p>

        {/* Create competition button */}
        <Button
          size="4"
          className="text-white px-8 py-3 font-semibold w-full max-w-sm"
          style={{
            background: 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)',
            boxShadow: '0 0 25px rgba(59, 130, 246, 0.6), 0 0 50px rgba(59, 130, 246, 0.4)',
            border: '1px solid rgba(59, 130, 246, 0.3)'
          }}
        >
          Create competition
        </Button>
      </div>

      {/* Bottom indicator */}
      <div className="flex justify-center pb-4 absolute bottom-0 w-full">
        <div className="w-32 h-1 bg-white/30 rounded-full"></div>
      </div>
    </div>
  );
}