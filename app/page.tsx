'use client';

import { But<PERSON>, Text, Heading } from 'frosted-ui';
import Link from 'next/link';

export default function Page() {
	return (
		<div className="min-h-screen bg-black text-white">
			{/* Top Blue Glow Navigation Bar */}
			<div className="relative bg-black border-b border-blue-500/30">
				<div className="absolute inset-0 bg-blue-500/10 blur-xl"></div>
				<div className="relative px-4 py-4">
					<Heading size="7" weight="bold" className="text-center text-white">
						Competition Platform
					</Heading>
				</div>
			</div>

			{/* Main Navigation Content */}
			<div className="max-w-md mx-auto px-4 py-8 space-y-6">
				<div className="text-center mb-8">
					<Text size="5" className="text-gray-300">Navigate to different sections</Text>
				</div>

				{/* Navigation Buttons */}
				<div className="space-y-4">
					<Link href="/leaderboard" className="block">
						<Button size="4" className="w-full">
							View Leaderboard
						</Button>
					</Link>

					<Link href="/competitions" className="block">
						<Button size="4" className="w-full">
							View Competitions
						</Button>
					</Link>

					<Link href="/competitions/with-data" className="block">
						<Button size="4" className="w-full">
							Competitions with Data
						</Button>
					</Link>

					<Link href="/create-competition" className="block">
						<Button size="4" variant="solid" className="w-full">
							Create Competition
						</Button>
					</Link>
				</div>

				{/* Info Section */}
				<div className="mt-12 text-center">
					<Text size="3" className="text-gray-400">
						Built with Whop React Components
					</Text>
				</div>
			</div>
		</div>
	);
}
