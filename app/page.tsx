'use client';

import { <PERSON><PERSON>, <PERSON>, Avatar, Badge } from 'frosted-ui';
import Link from 'next/link';

// Mock data for competition leaderboard
const competitionData = [
  {
    rank: 1,
    name: "<PERSON>",
    username: "@shark",
    avatar: "🦈",
    amount: 1794.80,
    color: "bg-yellow-500"
  },
  {
    rank: 2,
    name: "<PERSON>",
    username: "@methodicalstew",
    avatar: "👨‍💻",
    amount: 990.50,
    color: "bg-gray-400"
  },
  {
    rank: 3,
    name: "<PERSON>ha<PERSON>",
    username: "@shaq4257",
    avatar: "🏀",
    amount: 975.00,
    color: "bg-orange-500"
  },
  {
    rank: 4,
    name: "Savnatra",
    username: "@savnatra",
    avatar: "💎",
    amount: 800.80,
    color: "bg-blue-500"
  },
  {
    rank: 5,
    name: "<PERSON>",
    username: "@user673297",
    avatar: "🎯",
    amount: 610.00,
    color: "bg-purple-500"
  },
  {
    rank: 6,
    name: "<PERSON>",
    username: "@user673297",
    avatar: "🎯",
    amount: 540.80,
    color: "bg-pink-500"
  }
];

export default function Page() {
	return (
		<div className="min-h-screen bg-black text-white relative overflow-hidden">
			{/* Blue glow particles background */}
			<div className="absolute inset-0 overflow-hidden">
				<div className="absolute top-20 left-10 w-32 h-32 bg-blue-500/20 rounded-full blur-xl animate-pulse"></div>
				<div className="absolute top-40 right-20 w-24 h-24 bg-blue-400/15 rounded-full blur-lg animate-pulse delay-1000"></div>
				<div className="absolute bottom-40 left-1/4 w-40 h-40 bg-blue-600/10 rounded-full blur-2xl animate-pulse delay-2000"></div>
				<div className="absolute bottom-20 right-1/3 w-28 h-28 bg-blue-300/20 rounded-full blur-xl animate-pulse delay-500"></div>
				<div className="absolute top-1/2 left-1/2 w-36 h-36 bg-blue-500/10 rounded-full blur-2xl animate-pulse delay-3000"></div>
			</div>

			<div className="max-w-4xl mx-auto px-4 py-16 relative z-10">
				{/* Title with glow effect */}
				<div className="text-center mb-12">
					<h1 className="text-5xl font-bold text-white mb-4 relative">
						<span className="relative z-10">Competition Platform</span>
						<div className="absolute inset-0 text-blue-400 blur-lg opacity-50">Competition Platform</div>
					</h1>
					<p className="text-white/70 text-xl mb-8">Whoever makes the most money</p>

					{/* Competition info */}
					<div className="bg-white/5 backdrop-blur-sm rounded-xl p-6 border border-white/10 mb-8">
						<p className="text-red-400 text-sm mb-2">Ended on</p>
						<p className="text-white text-lg font-semibold">April 18th, 2025</p>
					</div>
				</div>

				{/* Participants section */}
				<div className="mb-8">
					<h2 className="text-2xl font-bold text-white mb-2">Participants</h2>
					<p className="text-white/60 text-sm mb-6">231</p>

					{/* Leaderboard */}
					<div className="space-y-3">
						{competitionData.map((user) => (
							<Card
								key={`${user.rank}-${user.username}`}
								className="bg-white/5 backdrop-blur-sm border-white/10 hover:bg-white/10 transition-colors p-4"
							>
								<div className="flex items-center justify-between">
									<div className="flex items-center space-x-4">
										<Badge className={`${user.color} text-white text-sm font-bold w-8 h-8 rounded-full flex items-center justify-center`}>
											{user.rank}
										</Badge>
										<Avatar
											className="w-10 h-10"
											fallback={user.avatar}
										/>
										<div>
											<div className="font-semibold text-white text-sm">{user.name}</div>
											<div className="text-xs text-white/60">{user.username}</div>
										</div>
									</div>
									<div className="flex items-center space-x-2">
										{/* Green dollar icon with glow */}
										<div className="relative">
											<svg className="w-4 h-4 text-green-400" fill="currentColor" viewBox="0 0 24 24">
												<path d="M7 15h2c0 1.08 1.37 2 3 2s3-.92 3-2c0-1.1-1.37-2-3-2H9c-2.76 0-5-2.24-5-5s2.24-5 5-5h2V3h2v2h2c0-1.08-1.37-2-3-2s-3 .92-3 2c0 1.1 1.37 2 3 2h3c2.76 0 5 2.24 5 5s-2.24 5-5 5h-2v2h-2v-2H7c0 1.08 1.37 2 3 2s3-.92 3-2z"/>
											</svg>
											<div className="absolute inset-0 text-green-400 blur-sm opacity-50">
												<svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
													<path d="M7 15h2c0 1.08 1.37 2 3 2s3-.92 3-2c0-1.1-1.37-2-3-2H9c-2.76 0-5-2.24-5-5s2.24-5 5-5h2V3h2v2h2c0-1.08-1.37-2-3-2s-3 .92-3 2c0 1.1 1.37 2 3 2h3c2.76 0 5 2.24 5 5s-2.24 5-5 5h-2v2h-2v-2H7c0 1.08 1.37 2 3 2s3-.92 3-2z"/>
												</svg>
											</div>
										</div>
										<span className="text-green-400 text-lg font-bold bg-gradient-to-r from-green-400 to-green-300 bg-clip-text text-transparent">
											${user.amount.toFixed(2)}
										</span>
									</div>
								</div>
							</Card>
						))}
					</div>
				</div>

				{/* Navigation buttons */}
				<div className="flex flex-col space-y-4">
					<Link href="/leaderboard">
						<Button className="w-full bg-blue-500 hover:bg-blue-600 text-white py-4 rounded-xl font-semibold text-lg relative overflow-hidden">
							<span className="relative z-10">View Full Leaderboard</span>
							<div className="absolute inset-0 bg-blue-400/20 blur-xl"></div>
						</Button>
					</Link>

					<Link href="/competitions">
						<Button className="w-full bg-blue-500 hover:bg-blue-600 text-white py-4 rounded-xl font-semibold text-lg relative overflow-hidden">
							<span className="relative z-10">View Competitions</span>
							<div className="absolute inset-0 bg-blue-400/20 blur-xl"></div>
						</Button>
					</Link>

					<Link href="/competitions/with-data">
						<Button className="w-full bg-blue-500 hover:bg-blue-600 text-white py-4 rounded-xl font-semibold text-lg relative overflow-hidden">
							<span className="relative z-10">Competitions with Data</span>
							<div className="absolute inset-0 bg-blue-400/20 blur-xl"></div>
						</Button>
					</Link>

					<Link href="/create-competition">
						<Button className="w-full bg-green-500 hover:bg-green-600 text-white py-4 rounded-xl font-semibold text-lg relative overflow-hidden">
							<span className="relative z-10">Create Competition</span>
							<div className="absolute inset-0 bg-green-400/20 blur-xl"></div>
						</Button>
					</Link>
				</div>
			</div>
		</div>
	);
}
